<!DOCTYPE html>
<html lang="zh-TW"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>CareComms - 首頁</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Roboto', sans-serif;}
        .gradient-bg {
            background: linear-gradient(120deg, #89f7fe 0%, #66a6ff 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-100">
<div class="container mx-auto max-w-md">
<header class="p-6 flex items-center">
<div class="bg-teal-500 p-2 rounded-full mr-3">
<span class="material-icons text-white text-xl">local_hospital</span>
</div>
<h1 class="text-2xl font-semibold text-gray-700">CareComms</h1>
</header>
<main class="p-4 space-y-6">
<section class="grid grid-cols-2 gap-4">
<div class="bg-white p-6 rounded-xl card-shadow flex flex-col items-center text-center space-y-3 hover:bg-gray-50 transition-colors">
<span class="material-icons text-4xl text-blue-500">person_outline</span>
<span class="text-gray-700 font-medium">個人資料</span>
</div>
<div class="bg-white p-6 rounded-xl card-shadow flex flex-col items-center text-center space-y-3 hover:bg-gray-50 transition-colors">
<span class="material-icons text-4xl text-purple-500">group_add</span>
<span class="text-gray-700 font-medium">編輯群組</span>
</div>
</section>
<section class="bg-white p-6 rounded-xl card-shadow space-y-4">
<h2 class="text-lg font-semibold text-gray-700 mb-4">快速操作</h2>
<a class="flex items-center p-3 rounded-lg hover:bg-gray-100 transition-colors" href="#">
<span class="material-icons text-teal-500 mr-4">search</span>
<span class="text-gray-600">搜尋使用者</span>
</a>
<a class="flex items-center p-3 rounded-lg hover:bg-gray-100 transition-colors" href="#">
<span class="material-icons text-indigo-500 mr-4">visibility</span>
<span class="text-gray-600">查看我發起的通知</span>
</a>
</section>
<section>
<button class="w-full gradient-bg text-white font-semibold py-4 px-6 rounded-xl card-shadow hover:opacity-90 transition-opacity flex items-center justify-center">
<span class="material-icons mr-2">send</span>
<span>發送新通知</span>
</button>
</section>
</main>
<nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 max-w-md mx-auto">
<div class="flex justify-around items-center h-16">
<a class="flex flex-col items-center text-blue-600" href="#">
<span class="material-icons">home</span>
<span class="text-xs">首頁</span>
</a>
<a class="flex flex-col items-center text-gray-500 hover:text-blue-600 transition-colors relative -top-4" href="#">
<div class="bg-blue-600 p-4 rounded-full text-white shadow-lg">
<span class="material-icons text-3xl">add_alert</span>
</div>
</a>
<a class="flex flex-col items-center text-gray-500 hover:text-blue-600 transition-colors" href="#">
<span class="material-icons">notifications</span>
<span class="text-xs">通知</span>
</a>
</div>
</nav>
</div>

</body></html>