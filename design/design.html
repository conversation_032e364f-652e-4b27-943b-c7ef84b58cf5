<!DOCTYPE html>
<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    .active-nav-item svg {
      color: #4F46E5;
    }
    .active-nav-item span {
      color: #4F46E5;
    }
    body {
      min-height: max(884px, 100dvh);
      background-color: #FFFFFF;
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body>
<div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden" style='font-family: Lexend, "Noto Sans", sans-serif; background-color: #FFFFFF;'>
<div class="flex-grow">
<header class="sticky top-0 z-10 bg-indigo-600 shadow-md">
<div class="flex items-center justify-between p-4 h-16">
<div class="flex items-center">
<img alt="App Logo" class="h-10 w-10 rounded-full border-2 border-white" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCFiSh23snutDa79cLr4-HtIJZ6A4tk8Wbqhy7HOW1-uH8cgVMVxnK0VCXWFNtMYLIvT9eYoiGhg97iHB5uFNyUqqAjgEaNGjpxIxUw72XXJ_sxHuiCgdTmxqss-M6poAh1cHmu4Ynndc2REpshiKY2UvnfNpo6fecP4ylaRIZfd-6v3sGqgo0wvzh4VLFbJVn9D1qHBoGJoz2FSzEaYzYNJ2CwGykhxOzfIVyYVzz0xnedvU3ZyCePOdzobyJtoz8mjPsEsqwd4Es"/>
<span class="ml-3 text-xl font-bold text-white">CareComms</span>
</div>
</div>
</header>
<main class="p-6 space-y-6">
<div class="grid grid-cols-2 gap-6">
<a class="flex flex-col items-center justify-center bg-slate-50 p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 aspect-square" href="#">
<div class="flex items-center justify-center size-14 bg-indigo-100 rounded-full mb-3">
<svg class="w-7 h-7 text-indigo-600" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>
</div>
<p class="text-slate-700 text-base font-medium text-center">Profile</p>
</a>
<a class="flex flex-col items-center justify-center bg-slate-50 p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 aspect-square" href="#">
<div class="flex items-center justify-center size-14 bg-sky-100 rounded-full mb-3">
<svg class="w-7 h-7 text-sky-600" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>
</div>
<p class="text-slate-700 text-base font-medium text-center">Edit Group</p>
</a>
</div>
<div class="bg-slate-50 p-6 rounded-xl shadow-md">
<h2 class="text-lg font-semibold text-slate-700 mb-4">Quick Actions</h2>
<div class="grid grid-cols-1 gap-4">
<button class="flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200">
<div class="flex items-center justify-center size-10 bg-purple-100 text-purple-600 rounded-full">
<svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>
</div>
<span class="text-purple-700 font-medium">Search User</span>
</button>
<button class="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200">
<div class="flex items-center justify-center size-10 bg-blue-100 text-blue-600 rounded-full">
<svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>
</div>
<span class="text-blue-700 font-medium">View My Initiated Notifications</span>
</button>
</div>
</div>
</main>
</div>
<footer class="sticky bottom-0 bg-white border-t border-slate-200 pb-safe-bottom shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.07),0_-2px_4px_-2px_rgba(0,0,0,0.04)]">
<nav class="flex justify-around items-center h-24 px-4">
<a class="flex flex-col items-center justify-center text-slate-500 hover:text-indigo-600 p-3 rounded-lg transition-colors duration-150 active-nav-item w-1/3 h-full" href="#">
<svg fill="currentColor" height="32px" viewBox="0 0 24 24" width="32px" xmlns="http://www.w3.org/2000/svg">
<path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8h5z"></path>
</svg>
<span class="text-sm font-medium mt-1.5">Home</span>
</a>
<a class="flex items-center justify-center bg-indigo-600 text-white rounded-full size-20 -mt-10 shadow-xl hover:bg-indigo-700 active:bg-indigo-800 transition-all duration-300 transform hover:scale-110 active:scale-95 mx-2" href="#">
<svg fill="currentColor" height="40px" viewBox="0 0 256 256" width="40px" xmlns="http://www.w3.org/2000/svg">
<path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
</svg>
</a>
<a class="flex flex-col items-center justify-center text-slate-500 hover:text-indigo-600 p-3 rounded-lg transition-colors duration-150 w-1/3 h-full" href="#">
<svg fill="currentColor" height="32px" viewBox="0 0 256 256" width="32px" xmlns="http://www.w3.org/2000/svg">
<path d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"></path>
</svg>
<span class="text-sm font-medium mt-1.5">Notifications</span>
</a>
</nav>
<div class="h-[env(safe-area-inset-bottom)] bg-white"></div>
</footer>
</div>

</body></html>