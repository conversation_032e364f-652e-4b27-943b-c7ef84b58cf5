<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>New Notification</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<style>
    body {
      font-family: 'Inter', sans-serif;
      min-height: max(884px, 100dvh);
    }
    .input-field {
      border: 1px solid #E5E7EB;
      padding: 0.75rem;
      border-radius: 0.375rem;
      width: 100%;
      transition: border-color 0.2s ease-in-out;
    }
    .input-field:focus {
      border-color: #3B82F6;
      outline: none;
    }
    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 600;
      transition: background-color 0.2s ease-in-out;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;}
    .btn-primary {
      background-color: #3B82F6;
      color: white;
    }
    .btn-primary:hover {
      background-color: #2563EB;
    }
    .btn-secondary {
      background-color: #F3F4F6;
      color: #374151;
      border: 1px solid #E5E7EB;
    }
    .btn-secondary:hover {
      background-color: #E5E7EB;
    }
    .btn-case-type {
        flex-direction: column;padding: 0.75rem 0.5rem;font-size: 0.75rem;text-align: center;
        min-width: 80px;}
    .btn-case-type .material-icons {
        font-size: 1.25rem;margin-bottom: 0.25rem;}
    .card {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      padding: 1.5rem;
    }
    .avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 9999px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: white;
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-slate-50 p-4 md:p-8">
<div class="max-w-md mx-auto">
<header class="mb-6 flex items-center justify-between">
<button class="text-slate-500 hover:text-slate-700">
<span class="material-icons text-2xl">close</span>
</button>
<h1 class="text-xl font-bold text-slate-800 text-center flex-grow">New Notification</h1>
<div class="w-8"></div> 
</header>
<main class="space-y-6">
<section class="card">
<h2 class="text-base font-semibold text-slate-700 mb-3">Case Type</h2>
<div class="flex space-x-2 overflow-x-auto pb-2">
<button class="btn btn-secondary btn-case-type ring-2 ring-blue-500 bg-blue-50 border-blue-500 text-blue-600 flex-shrink-0">
<span class="material-icons">family_restroom</span>
<span class="whitespace-nowrap">Mother &amp; Baby</span>
</button>
<button class="btn btn-secondary btn-case-type flex-shrink-0">
<span class="material-icons">person</span>
<span class="whitespace-nowrap">Mother Only</span>
</button>
<button class="btn btn-secondary btn-case-type flex-shrink-0">
<span class="material-icons">baby_changing_station</span>
<span class="whitespace-nowrap">Baby to NICU</span>
</button>
</div>
</section>
<section class="card" id="case-details-section">
<div>
<h2 class="text-base font-semibold text-slate-700 mb-4">Mother &amp; Baby Details</h2>
<div class="space-y-4">
<div>
<label class="block text-xs font-medium text-slate-600 mb-1" for="mother-initial">Mother's Initial <span class="text-red-500">*</span></label>
<input class="input-field text-sm" id="mother-initial" placeholder="Enter initial" type="text"/>
</div>
<div>
<label class="block text-xs font-medium text-slate-600 mb-1" for="mother-bed-number">Mother's Bed Number <span class="text-red-500">*</span></label>
<input class="input-field text-sm" id="mother-bed-number" placeholder="Enter bed number" type="text"/>
</div>
<div>
<label class="block text-xs font-medium text-slate-600 mb-1" for="designated-ward">Designated Ward <span class="text-red-500">*</span></label>
<div class="relative">
<select class="input-field appearance-none bg-teal-50 border-teal-300 text-teal-700 focus:ring-teal-500 focus:border-teal-500 text-sm" id="designated-ward">
<option>Ward A</option>
<option>Ward B</option>
<option>NICU</option>
</select>
<div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-teal-700">
<span class="material-icons text-lg">arrow_drop_down</span>
</div>
</div>
</div>
</div>
</div>
<div class="mt-4 border-t border-slate-200 pt-4">
<button class="w-full flex justify-between items-center text-left py-2 px-3 rounded-md hover:bg-slate-100 transition-colors duration-150 focus:outline-none focus:ring-1 focus:ring-blue-400" id="toggle-clinical-notes">
<span class="font-medium text-slate-700 text-sm">Add Clinical Notes (optional)</span>
<span class="material-icons text-slate-500 transform transition-transform duration-300 text-xl" id="notes-indicator">expand_more</span>
</button>
<div class="hidden mt-3" id="clinical-notes-section">
<textarea class="input-field text-sm" id="clinical-notes" placeholder="Enter clinical notes (optional)..." rows="3"></textarea>
<p class="mt-1 text-xs text-slate-500">Please provide any relevant clinical information.</p>
</div>
</div>
</section>
<section class="card">
<h2 class="text-base font-semibold text-slate-700 mb-3">Recipients</h2>
<div class="space-y-2.5">
<div class="flex items-center space-x-3 p-2.5 bg-slate-50 rounded-md">
<div class="avatar bg-indigo-500 text-sm">JD</div>
<div>
<p class="font-medium text-slate-800 text-sm">Jane Doe</p>
</div>
</div>
<div class="flex items-center space-x-3 p-2.5 bg-slate-50 rounded-md">
<div class="avatar bg-sky-500 text-sm">SM</div>
<div>
<p class="font-medium text-slate-800 text-sm">Sarah Miller</p>
</div>
</div>
<div class="flex items-center space-x-3 p-2.5 bg-slate-50 rounded-md">
<div class="avatar bg-emerald-500 text-sm">PA</div>
<div>
<p class="font-medium text-slate-800 text-sm">Paul Allen</p>
</div>
</div>
</div>
</section>
<div class="flex flex-col space-y-3 pt-2">
<button class="btn btn-secondary w-full text-sm">
<span class="material-icons text-lg">group_add</span>
<span>Select Recipients</span>
</button>
<button class="btn btn-primary w-full text-sm">
          Send Notification
        </button>
</div>
</main>
</div>
<script>
    const toggleButton = document.getElementById('toggle-clinical-notes');
    const notesSection = document.getElementById('clinical-notes-section');
    const notesIndicator = document.getElementById('notes-indicator');
    const clinicalNotesTextarea = document.getElementById('clinical-notes');
    toggleButton.addEventListener('click', () => {
      const isExpanded = notesSection.classList.toggle('hidden');
      notesIndicator.textContent = isExpanded ? 'expand_more' : 'expand_less';
      toggleButton.setAttribute('aria-expanded', !isExpanded);
      if (!isExpanded) { 
        clinicalNotesTextarea.focus();
        toggleButton.classList.add('bg-blue-50', 'ring-1', 'ring-blue-300');
        notesIndicator.classList.add('text-blue-600');
      } else {
        toggleButton.classList.remove('bg-blue-50', 'ring-1', 'ring-blue-300');
        notesIndicator.classList.remove('text-blue-600');
      }
    });
    // Placeholder for dynamically changing card content based on Case Type
    // This would typically involve more complex JS to swap out the content of #case-details-section
    const caseTypeButtons = document.querySelectorAll('.btn-case-type');
    const caseDetailsSection = document.getElementById('case-details-section');
    const motherBabyDetailsContainer = caseDetailsSection.querySelector('div:first-child > div.space-y-4'); // Assuming this is the container for the fields
    caseTypeButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active state from all buttons
            caseTypeButtons.forEach(btn => {
                btn.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'border-blue-500', 'text-blue-600');
                btn.classList.add('btn-secondary'); // Ensure it reverts to default secondary style
            });
            // Add active state to clicked button
            button.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50', 'border-blue-500', 'text-blue-600');
            button.classList.remove('btn-secondary');
            const caseType = button.querySelector('span:last-child').textContent.trim();
            updateCaseDetails(caseType);
        });
    });
    function updateCaseDetails(caseType) {
        const titleElement = caseDetailsSection.querySelector('h2');
        const motherInitialField = motherBabyDetailsContainer.children[0];
        const motherBedNumberField = motherBabyDetailsContainer.children[1];
        const designatedWardField = motherBabyDetailsContainer.children[2];
        // Reset fields visibility
        motherInitialField.style.display = 'none';
        motherBedNumberField.style.display = 'none';
        designatedWardField.style.display = 'none';
        if (titleElement) {
            if (caseType === "Mother & Baby") {
                titleElement.textContent = "Mother & Baby Details";
                motherInitialField.style.display = 'block';
                motherBedNumberField.style.display = 'block';
                designatedWardField.style.display = 'block';
            } else if (caseType === "Mother Only") {
                titleElement.textContent = "Mother's Details";
                motherInitialField.style.display = 'block';
                motherBedNumberField.style.display = 'block';
                designatedWardField.style.display = 'block';
            } else if (caseType === "Baby to NICU") {
                titleElement.textContent = "Baby to NICU Details";
                 // For "Baby to NICU", we'll just show designated ward as an example
                motherInitialField.style.display = 'none'; // Assuming initial for baby or specific fields
                motherBedNumberField.style.display = 'none'; // Assuming bed no. for baby or specific fields
                designatedWardField.style.display = 'block';
                 // You would add baby-specific fields here
                const babyIdLabel = motherBabyDetailsContainer.querySelector('label[for="baby-id"]');
                if (babyIdLabel) babyIdLabel.parentElement.style.display = 'block';
            } else if (caseType === "Baby Only") {
                titleElement.textContent = "Baby's Details";
                // Example: Show specific fields for baby only
                motherInitialField.style.display = 'none'; // Might rename this to 'Baby's Initial'
                motherBedNumberField.style.display = 'none'; // Might rename this to 'Baby's Location/Bed'
                designatedWardField.style.display = 'block';
                const babyIdLabel = motherBabyDetailsContainer.querySelector('label[for="baby-id"]');
                if (babyIdLabel) babyIdLabel.parentElement.style.display = 'block';
            } else {
                titleElement.textContent = `${caseType} Details`;
                // For "Other", you might show a generic notes field or a different set of inputs
                // For simplicity, hide all specific fields for now
            }
        }
        console.log("Selected Case Type:", caseType);
    }
     // Initial call to set up fields based on default selected case type
    document.addEventListener('DOMContentLoaded', () => {
        const defaultActiveButton = document.querySelector('.btn-case-type.ring-2.ring-blue-500');
        if (defaultActiveButton) {
            const defaultCaseType = defaultActiveButton.querySelector('span:last-child').textContent.trim();
            updateCaseDetails(defaultCaseType);
        }
    });
  </script>

</body></html>