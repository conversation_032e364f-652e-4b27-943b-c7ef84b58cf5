import { MaterialIcons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import * as Haptics from 'expo-haptics';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { useTheme } from 'react-native-paper';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';

// 自定義中央浮動按鈕組件 - 帶觸控動畫效果
function AnimatedFloatingActionButton({ onPress }: { onPress?: () => void }) {
  const theme = useTheme();
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);

  // 觸控動畫樣式
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` }
      ],
    };
  });

  // 處理按下事件 - 實現 scale + shake 動畫組合
  const handlePressIn = () => {
    // 觸覺回饋
    runOnJS(() => {
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    })();

    // Scale 動畫 + 輕微震動效果
    scale.value = withSpring(1.1, { damping: 15, stiffness: 300 });
    rotation.value = withSequence(
      withSpring(-2, { damping: 15, stiffness: 400 }),
      withSpring(2, { damping: 15, stiffness: 400 }),
      withSpring(0, { damping: 15, stiffness: 400 })
    );
  };

  // 處理釋放事件
  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  // 處理點擊事件 - 修復導航功能
  const handlePress = () => {
    console.log('Add button pressed - navigating to add screen');
    // 調用傳入的 onPress 回調來觸發導航
    if (onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <Animated.View style={[
        styles.floatingButton,
        { backgroundColor: theme.colors.primary },
        animatedStyle
      ]}>
        <MaterialIcons name="add" size={32} color={theme.colors.onPrimary} />
      </Animated.View>
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          height: 96, // 增加高度以容納浮動按鈕
          paddingBottom: Platform.OS === 'ios' ? 34 : 16, // 考慮安全區域
          paddingTop: 16,
          backgroundColor: theme.colors.surface,
          borderTopWidth: 1,
          borderTopColor: theme.colors.outline,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -4,
          },
          shadowOpacity: 0.1,
          shadowRadius: 6,
          ...Platform.select({
            ios: {
              position: 'absolute',
            },
            default: {},
          }),
        },
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '500',
          marginTop: 6,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ focused }) => (
            <MaterialIcons
              name="home"
              size={32}
              color={focused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="add"
        options={{
          title: '',
          tabBarIcon: () => null, // 不顯示默認圖標
          tabBarButton: (props) => (
            <View style={styles.floatingButtonContainer}>
              <View style={styles.floatingButtonWrapper}>
                <AnimatedFloatingActionButton onPress={() => props.onPress && props.onPress({} as any)} />
              </View>
            </View>
          ),
        }}
        listeners={{
          tabPress: () => {
            console.log('Add button pressed - navigating to add screen');
            // 移除 preventDefault，允許正常導航到 add 頁面
          },
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Notifications',
          tabBarIcon: ({ focused }) => (
            <MaterialIcons
              name="notifications"
              size={32}
              color={focused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          ),
        }}
      />
    </Tabs>
  );
}

// 樣式定義 - 使用主題顏色支持 Dark Mode
const styles = StyleSheet.create({
  floatingButtonContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  floatingButtonWrapper: {
    marginTop: -40, // 向上偏移以創建浮動效果
  },
  floatingButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    // backgroundColor 通過主題顏色動態設置
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});
