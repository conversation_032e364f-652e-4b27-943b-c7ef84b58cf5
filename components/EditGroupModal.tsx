import { MaterialIcons } from '@expo/vector-icons';
import React, { useState, useEffect, useCallback } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, TouchableOpacity, View } from 'react-native';
import {
  Appbar,
  Button,
  Divider,
  Modal,
  Portal,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';

// Available staff members for group management
const AVAILABLE_STAFF = [
  { id: '1', name: '<PERSON>', role: 'Obstetrician', initials: 'J<PERSON>', color: '#3B82F6' },
  { id: '2', name: '<PERSON>', role: 'Midwife', initials: '<PERSON>', color: '#8B5CF6' },
  { id: '3', name: '<PERSON>', role: 'Anaesthetist', initials: '<PERSON>', color: '#10B981' },
  { id: '4', name: '<PERSON>', role: 'Nurse', initials: '<PERSON>', color: '#F59E0B' },
  { id: '5', name: '<PERSON>', role: 'Pediatrician', initials: '<PERSON>', color: '#EF4444' },
  { id: '6', name: '<PERSON>', role: '<PERSON><PERSON>', initials: 'E<PERSON>', color: '#06B6D4' },
  { id: '7', name: '<PERSON> <PERSON>', role: 'Nurse', initials: 'CS', color: '#F97316' },
  { id: '8', name: 'Michael Wilson', role: 'Technician', initials: 'MW', color: '#EC4899' },
];

// Default group for editing (can be passed as prop in real implementation)
const DEFAULT_GROUP = {
  id: 'all_employees',
  name: 'All Employees',
  memberIds: ['4', '5', '6'], // Alice Johnson, Robert Brown, Emily Davis
};

interface EditGroupModalProps {
  visible: boolean;
  onDismiss: () => void;
  onSave?: (groupData: { name: string; memberIds: string[] }) => void;
  onDelete?: () => void;
}

// Edit Group modal component - based on EditGroup.html patterns
export default function EditGroupModal({
  visible,
  onDismiss,
  onSave,
  onDelete,
}: EditGroupModalProps) {
  // Get current theme to support Dark Mode
  const theme = useTheme();
  
  // Form state
  const [groupName, setGroupName] = useState(DEFAULT_GROUP.name);
  const [memberIds, setMemberIds] = useState<string[]>(DEFAULT_GROUP.memberIds);
  const [isClosing, setIsClosing] = useState(false); // 追蹤關閉狀態以防止閃爍

  // 重置表單狀態到初始值
  const resetFormState = useCallback(() => {
    setGroupName(DEFAULT_GROUP.name);
    setMemberIds(DEFAULT_GROUP.memberIds);
    setIsClosing(false);
  }, []);

  // 當 modal 顯示狀態改變時重置表單
  useEffect(() => {
    if (visible) {
      // Modal 打開時重置狀態
      setIsClosing(false); // 確保 Modal 可見時 isClosing 為 false
      resetFormState();
    }
  }, [visible, resetFormState]);

  // Get current members and available members
  const currentMembers = AVAILABLE_STAFF.filter(staff => memberIds.includes(staff.id));
  const availableMembers = AVAILABLE_STAFF.filter(staff => !memberIds.includes(staff.id));

  // Handle adding member to group
  const handleAddMember = useCallback((staffId: string) => {
    if (!isClosing) {
      setMemberIds(prev => [...prev, staffId]);
    }
  }, [isClosing]);

  // Handle removing member from group
  const handleRemoveMember = useCallback((staffId: string) => {
    if (!isClosing) {
      setMemberIds(prev => prev.filter(id => id !== staffId));
    }
  }, [isClosing]);

  // 優化的關閉處理函數，防止閃爍 - 修復動畫衝突
  const handleDismiss = useCallback(() => {
    if (!isClosing) {
      setIsClosing(true);
      // 使用 requestAnimationFrame 而非 setTimeout 避免 Hermes 動畫衝突
      requestAnimationFrame(() => {
        console.log("onDismiss");
        onDismiss();
      });
      
      // setTimeout(() => {
      //   console.log("onDismiss");
      //       onDismiss(); // 調用父組件傳遞的 onDismiss
      //       // isClosing 狀態會在 Modal 重新打開時由 useEffect 重置
      //   }, 1000); // 嘗試一個大約的動畫時長，例如 250ms，需要根據實際情況調整

    }
  }, [isClosing, onDismiss]);

  // Handle save - 優化狀態更新順序，修復動畫衝突
  const handleSave = useCallback(() => {
    if (!isClosing && groupName.trim()) {
      setIsClosing(true);

      // 先執行保存回調
      if (onSave) {
        onSave({ name: groupName, memberIds });
      }

      // 使用 requestAnimationFrame 避免動畫衝突
      requestAnimationFrame(() => {
        onDismiss();
      });
    }
  }, [isClosing, groupName, memberIds, onSave, onDismiss]);

  // Handle delete - 優化狀態更新順序，修復動畫衝突
  const handleDelete = useCallback(() => {
    if (!isClosing) {
      setIsClosing(true);

      // 先執行刪除回調
      if (onDelete) {
        onDelete();
      }

      // 使用 requestAnimationFrame 避免動畫衝突
      requestAnimationFrame(() => {
        onDismiss();
      });
    }
  }, [isClosing, onDelete, onDismiss]);

  return (
    <Portal>
      <Modal
        visible={visible && !isClosing} // 防止關閉過程中的閃爍
        // visible={visible}
        onDismiss={handleDismiss} // 使用優化的關閉處理函數
        contentContainerStyle={[
          styles.modalContainer,
          { backgroundColor: theme.colors.surface }
        ]}
        dismissable={!isClosing} // 關閉過程中禁用手勢關閉
        dismissableBackButton={!isClosing} // 關閉過程中禁用返回鍵關閉
      
      >
        {/* Header - 優化高度與主頁面保持一致 */}
        <Appbar.Header style={[styles.header, { backgroundColor: theme.colors.surface }]}>
          <Appbar.BackAction onPress={handleDismiss} />
          <Appbar.Content
            title="Edit Group"
            titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
          />
        </Appbar.Header>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Group Name Input */}
          <View style={styles.section}>
            <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>
              Group Name
            </Text>
            <TextInput
              value={groupName}
              onChangeText={setGroupName}
              style={[styles.groupNameInput, { backgroundColor: theme.colors.surface }]}
              mode="outlined"
              placeholder="Enter group name"
              dense
            />
          </View>

          {/* Current Members Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Members ({currentMembers.length})
              </Text>
            </View>
            {currentMembers.map((member) => (
              <Surface
                key={member.id}
                style={[
                  styles.memberItem,
                  { backgroundColor: theme.colors.surfaceVariant }
                ]}
                elevation={1}
              >
                <View style={styles.memberContent}>
                  <View style={styles.memberLeft}>
                    <View style={[styles.memberAvatar, { backgroundColor: member.color }]}>
                      <RNText style={styles.memberInitials}>{member.initials}</RNText>
                    </View>
                    <Text style={[styles.memberText, { color: theme.colors.onSurface }]}>
                      {member.name}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveMember(member.id)}
                    style={styles.actionButton}
                  >
                    <MaterialIcons name="remove-circle-outline" size={24} color={theme.colors.error} />
                  </TouchableOpacity>
                </View>
              </Surface>
            ))}
          </View>

          {/* Available Members Section */}
          {availableMembers.length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Other Contacts ({availableMembers.length})
              </Text>
              {availableMembers.map((member) => (
                <Surface
                  key={member.id}
                  style={[
                    styles.memberItem,
                    { backgroundColor: theme.colors.surface }
                  ]}
                  elevation={1}
                >
                  <View style={styles.memberContent}>
                    <View style={styles.memberLeft}>
                      <View style={[styles.memberAvatar, { backgroundColor: member.color }]}>
                        <RNText style={styles.memberInitials}>{member.initials}</RNText>
                      </View>
                      <Text style={[styles.memberText, { color: theme.colors.onSurface }]}>
                        {member.name}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => handleAddMember(member.id)}
                      style={styles.actionButton}
                    >
                      <MaterialIcons name="add-circle-outline" size={24} color={theme.colors.primary} />
                    </TouchableOpacity>
                  </View>
                </Surface>
              ))}
            </View>
          )}
        </ScrollView>

        {/* Bottom Actions */}
        <View style={styles.bottomContainer}>
          <Divider style={{ backgroundColor: theme.colors.outline }} />
          
          {/* Delete Button */}
          <Button
            mode="outlined"
            onPress={handleDelete}
            style={[
              styles.deleteButton,
              {
                borderColor: theme.colors.error,
                backgroundColor: theme.dark ? 'rgba(244, 67, 54, 0.1)' : '#FFEBEE'
              }
            ]}
            contentStyle={styles.deleteButtonContent}
            labelStyle={[styles.deleteButtonLabel, { color: theme.colors.error }]}
            disabled={isClosing} // 關閉過程中禁用按鈕
            icon={({ size }) => (
              <MaterialIcons name="delete-outline" size={size} color={theme.colors.error} />
            )}
          >
            Delete Group
          </Button>

          {/* Save Button */}
          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            contentStyle={styles.saveButtonContent}
            labelStyle={[styles.saveButtonLabel, { color: theme.colors.onPrimary }]}
            disabled={!groupName.trim() || isClosing} // 關閉過程中禁用按鈕
          >
            Save
          </Button>
        </View>
      </Modal>
    </Portal>
  );
}

// Style definitions - using theme colors to support Dark Mode and platform-specific fonts
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    margin: 0,
  },
  header: {
    // 與主頁面保持一致的 header 樣式
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  groupNameInput: {
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  memberItem: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
  },
  memberContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  memberText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  actionButton: {
    padding: 4,
  },
  bottomContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16,
  },
  deleteButton: {
    borderRadius: 12,
    marginTop: 12,
    marginBottom: 8,
  },
  deleteButtonContent: {
    paddingVertical: 8,
  },
  deleteButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  saveButton: {
    borderRadius: 12,
    marginTop: 8,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
  saveButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
});
