{"name": "qmnotiaugment", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "firebase-test": "node ./scripts/firebaseTest.js", "type-check": "tsc --noEmit", "build:android": "expo run:android"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-vector-icons/material-design-icons": "^12.0.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/elements": "^2.4.2", "@react-navigation/native": "^7.1.9", "@types/uuid": "^10.0.0", "expo": "~53.0.9", "expo-application": "^6.1.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "firebase": "^11.8.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/react": "~19.0.14", "@types/react-native-vector-icons": "^6.4.18", "eslint": "^9.27.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}